; Configuration file for RS485 application
; Generated at Jun 12 2025 09:35:09

[ProtocolList]
list=RS485,DCS_RS485

[RS485]
Port = COM5
BaudRate = 9600
StopBits = 2
Parity = N
DataBits = 8
Timeout = 1.0

[DCS_RS485]
Port = COM6
BaudRate = 9600
StopBits = 1
Parity = N
DataBits = 8
Timeout = 2.0

;Boiler name list
[BoilerList]
list=#3机组

[#3机组]
Desc = this is aboiler1
Protocol = RS485
CollectionInterval = 3
Nox = 20        ; SO2
Current =60     ; 抽气泵电流传感器设备地址，读取值×0.001，单位：A
So2 = 30        ; SO2
Voltage = 50    ; 压力表传感器设备地址，读取值×0.001，单位：kPa
O2 = 4          ; O2（氧气）传感器设备地址，读取值×0.01，单位：%
Co = 1          ; CO（一氧化碳）传感器设备地址，直接使用原始值，单位：ppm
Tempature = 40  ; 冷凝器温度传感器设备地址，读取值×0.1，单位：℃


;DCS设备列表
[DCSList]
list=DCS1

[DCS1]
Desc = 分布式控制系统1
Protocol = DCS_RS485
CollectionInterval = 10
DeviceAddress = 12
StartRegister = 31
RegisterCount = 44
AssociatedBoiler = #3机组
; 数据映射配置 - 基于88字节数据的字节偏移量
; 报文格式：7字节头部 + 88字节数据，数据从第8字节开始
FurnacePressureOffset = 0    ; 炉膛压力，字节偏移0 (对应报文位置8-11: 40 FD 69 66)
SuperheaterTempOffset = 4    ; 过热器平均温度，字节偏移4 (对应报文位置12-15: 41 D6 54 9C)
GeneratorPowerOffset = 8     ; 发电机功率，字节偏移8 (对应报文位置16-19: 3E F3 BF A2)
MainSteamPressureOffset = 12 ; 主蒸汽压力，字节偏移12 (对应报文位置20-23: 3E B4 B3 ED)
TotalAirFlowOffset = 16      ; 总风量，字节偏移16 (对应报文位置24-27: 43 7B 43 23)
WaterCoalRatioOffset = 24    ; 水煤比，字节偏移24 (对应报文位置32-35: 3F 18 D9 D2)
PrimaryFanAOffset = 28       ; 一次分机A入口导叶位置，字节偏移28 (对应报文位置36-39)
PrimaryFanBOffset = 32       ; 一次分机B入口导叶位置，字节偏移32 (对应报文位置40-43)
FanAOffset = 36              ; 送风机A入口导叶位置，字节偏移36 (对应报文位置44-47)
FanBOffset = 40              ; 送风机B入口导叶位置，字节偏移40 (对应报文位置48-51)
InducedFanAOffset = 44       ; 引风机A入口导叶位置，字节偏移44 (对应报文位置52-55)
InducedFanBOffset = 48       ; 引风机B入口导叶位置，字节偏移48 (对应报文位置56-59)
COOffset = 52                ; CO，字节偏移52 (对应报文位置60-63: 41 30 00 00)
O2Offset = 56                ; O2，字节偏移56 (对应报文位置64-67: 41 A3 1E B8)
SO2Offset = 60               ; SO2，字节偏移60 (对应报文位置68-71)
NOxOffset = 64               ; NOx，字节偏移64 (对应报文位置72-75)



