@echo off
chcp 65001 >nul
title Restart PowerPlant System
set "exeName=SmartBurning.exe"

echo [%time%] Restarting program...
echo [%time%] Current dir: %cd%
echo [%time%] Executable: %exeName%
echo [%time%] Full path: %cd%\%exeName%

if exist "%cd%\%exeName%" (
    echo [%time%] File exists, continuing...
) else (
    echo [%time%] ERROR: File not found %cd%\%exeName%
    pause
    exit /b 1
)

echo [%time%] Killing existing process...
taskkill /f /im "%exeName%" 2>nul
if %errorlevel% equ 0 (
    echo [%time%] Process killed
) else (
    echo [%time%] No running process found or kill failed
)

timeout /t 2 /nobreak >nul

echo [%time%] Starting new process...
start "" "%cd%\%exeName%"
if %errorlevel% equ 0 (
    echo [%time%] Process started successfully
) else (
    echo [%time%] Process start failed, error code: %errorlevel%
    pause
    exit /b 1
)

echo [%time%] Restart completed
timeout /t 3 /nobreak >nul
