import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtCharts

Page {
    id: monitoringSystem
    
    // 信号定义
    signal navigateBack()

    // 分解炉选择按钮组
    ButtonGroup {
        id: boilerButtonGroup
        exclusive: true
    }

    // 返回首页状态标志
    property bool isReturningHome: false

    // 添加回可能被引用的属性（简化版本，避免程序崩溃）
    property bool isBoilerSwitching: false
    property string pendingBoilerSwitch: ""
    property string selectedBoilerUI: ""

    // 时间范围选择相关属性
    property int currentTimeRange: 60  // 默认1小时
    property var timeRangeOptions: [
        {label: "1小时", minutes: 60},
        {label: "8小时", minutes: 480},
        {label: "12小时", minutes: 720},
        {label: "24小时", minutes: 1440}
    ]

    // 历史数据相关属性
    property bool isHistoryMode: false
    property date currentHistoryDate: new Date()
    property int historyDataPointCount: 0
    property double sliderPosition: 0.0  // 滑块位置 0.0-1.0

    // 智能图表更新定时器 - 统一的更新机制
    Timer {
        id: chartUpdateTimer
        interval: {
            // 根据当前设备的采集间隔动态调整更新频率
            var collectionInterval = monitorWindow.dataSource.getCurrentCollectionInterval()
            return Math.max(collectionInterval * 1000, 3000)  // 最少3秒更新一次
        }
        repeat: true
        running: monitorWindow.dataSource.isRunning

        property int updateCounter: 0  // 更新计数器

        onTriggered: {
            updateCounter++

            // 每10次增量更新后执行一次全量更新，避免数据偏差
            if (updateCounter % 10 === 0) {
                fullUpdateChartData()  // 全量更新
            } else {
                updateChartData()  // 增量更新
            }
        }
    }

    // 智能增量更新 - 避免不必要的重绘
    function updateChartData() {
        // 如果在历史模式下，不进行实时数据更新
        if (isHistoryMode) {
            return
        }

        if (monitorWindow.dataSource.currentBoiler !== "" &&
            monitorWindow.dataSource.isDataConnected) {
            // 使用优化的增量更新，传入当前时间范围
            monitorWindow.dataSource.updateChartIncremental(
                smokeO2Series, smokeCOSeries, currentTimeRange
            )
        }
    }

    // 全量更新函数 - 仅在必要时使用
    function fullUpdateChartData() {
        // 如果在历史模式下，不进行实时数据更新
        if (isHistoryMode) {
            return
        }

        if (monitorWindow.dataSource.currentBoiler !== "" &&
            monitorWindow.dataSource.isDataConnected) {
            // 全量更新，使用高效的replace方法，传入当前时间范围
            monitorWindow.dataSource.updateSmokeChartSeriesWithMinutes(
                smokeO2Series, smokeCOSeries, currentTimeRange
            )
        }
    }

    // 时间范围切换函数
    function switchTimeRange(newTimeRange) {
        if (currentTimeRange !== newTimeRange) {
            currentTimeRange = newTimeRange
            // 更新图表轴范围
            smokeAxisX.max = newTimeRange
            // tickCount会通过ValueAxis中的绑定自动更新，不需要手动设置
            // 强制全量更新图表数据
            if (isHistoryMode) {
                updateHistoryChart()
            } else {
                fullUpdateChartData()
            }
        }
    }

    // 计算当前时间范围需要的数据点数（使用预定义的缓冲区大小）
    function getPointsNeededForTimeRange(timeRangeMinutes) {
        switch (timeRangeMinutes) {
            case 60:   return 1200   // 1小时
            case 480:  return 9600   // 8小时
            case 720:  return 14400  // 12小时
            case 1440: return 28800  // 24小时
            default:   return 1200   // 默认1小时
        }
    }

    // 判断是否需要显示滑块
    function shouldShowSlider() {
        if (!isHistoryMode || historyDataPointCount === 0) {
            return false
        }
        var pointsNeeded = getPointsNeededForTimeRange(currentTimeRange)
        return historyDataPointCount > pointsNeeded
    }

    // 更新历史数据图表
    function updateHistoryChart() {
        if (isHistoryMode) {
            console.log("开始更新历史图表，时间范围:", currentTimeRange, "滑块位置:", sliderPosition)
            var startTime = Date.now()
            monitorWindow.dataSource.updateHistoryChartWithSlider(
                smokeO2Series, smokeCOSeries, currentTimeRange, sliderPosition
            )
            var endTime = Date.now()
            console.log("历史图表更新完成，耗时:", (endTime - startTime), "ms")
        }
    }

    // 加载历史数据
    function loadHistoryData(dateStr) {
        console.log("加载历史数据:", dateStr)

        // 将字符串转换为QDate
        var dateParts = dateStr.split('-')
        if (dateParts.length !== 3) {
            console.log("日期格式错误:", dateStr)
            return
        }

        var qdate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2])

        if (monitorWindow.dataSource.loadHistoryData(qdate)) {
            isHistoryMode = true
            currentHistoryDate = qdate
            // 从dataSource获取已加载的数据点数，避免重复读取CSV
            historyDataPointCount = monitorWindow.dataSource.getHistoryDataPointCount(qdate)
            sliderPosition = 1.0  // 默认显示最新数据
            console.log("历史数据点数:", historyDataPointCount)
            updateHistoryChart()
        } else {
            console.log("加载历史数据失败")
        }
    }

    // 切换到实时模式
    function switchToRealTimeMode() {
        isHistoryMode = false
        historyDataPointCount = 0
        sliderPosition = 0.0

        // 先清空图表
        smokeO2Series.clear()
        smokeCOSeries.clear()

        // 切换数据源到实时模式
        monitorWindow.dataSource.switchToRealTimeMode()

        // 重新加载实时数据
        Qt.callLater(function() {
            fullUpdateChartData()
        })
    }

    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回首页"
                enabled: !isReturningHome
                onClicked: {
                    if (isReturningHome) return

                    isReturningHome = true

                    // 直接触发页面切换
                    stackView.pop()

                    // 简单的清理操作
                    Qt.callLater(function() {
                        performQuickCleanup()
                    })
                }
            }
            Label {
                text: "锅炉燃烧数据监控系统"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }

            Button {
                text: "采集配置"
                onClicked: {
                    stackView.push(configPage)
                }
                background: Rectangle {
                    color: parent.pressed ? "#1976d2" : "#2196f3"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Button {
                text: monitorWindow.dataSource.isRunning ? "停止监控" : "开始监控"
                onClicked: {
                    if (monitorWindow.dataSource.isRunning) {
                        monitorWindow.stopMonitoring()
                    } else {
                        monitorWindow.startMonitoring()
                    }
                }
                background: Rectangle {
                    color: monitorWindow.dataSource.isRunning ? "#f44336" : "#4caf50"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Button {
                text: "清空数据"
                onClicked: monitorWindow.clearAllData()
                background: Rectangle {
                    color: parent.pressed ? "#ff9800" : "#ffc107"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        // 主要内容区域 - 水平布局
        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 20

            // 烟气数据区域 - 现在占据全宽
            ScrollView {
                Layout.fillWidth: true
                Layout.fillHeight: true

                // 限制外层滚动方向，只允许垂直滚动
                ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                ScrollBar.vertical.policy: ScrollBar.AsNeeded

                // 设置滚动行为
                contentWidth: -1  // 禁用水平滚动
                clip: true

                ColumnLayout {
                    width: parent.width
                    spacing: 20

                    // 锅炉选择区域 - 已注释
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 20

                            Label {
                                text: "选择锅炉:"
                                font.pixelSize: 16
                                font.bold: true
                                color: "#333333"
                            }

                            // 动态生成锅炉按钮
                            Repeater {
                                model: monitorWindow.dataSource.boilerList

                                Button {
                                    text: modelData
                                    Layout.preferredWidth: 100
                                    Layout.preferredHeight: 40
                                    checkable: true
                                    // 使用本地UI状态来控制选中状态，提供即时反馈
                                    checked: selectedBoilerUI === modelData
                                    ButtonGroup.group: boilerButtonGroup

                                    background: Rectangle {
                                        color: parent.checked ? "#E3F2FD" : "#ffffff"
                                        border.color: parent.checked ? "#2196f3" : "#ddd"
                                        border.width: 2
                                        radius: 8
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: parent.checked ? "#2196f3" : "#333333"
                                        font.pixelSize: 14
                                        font.bold: parent.checked
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }

                                    onClicked: {
                                        // 简化：直接更新锅炉选择
                                        selectedBoilerUI = modelData
                                        monitorWindow.dataSource.currentBoiler = modelData
                                    }
                                }
                            }

                            // 历史数据查看区域
                            RowLayout {
                                spacing: 10

                                Label {
                                    text: "历史数据:"
                                    font.pixelSize: 14
                                    color: "#666666"
                                }

                                ComboBox {
                                    id: historicalDateCombo
                                    Layout.preferredWidth: 120
                                    Layout.preferredHeight: 30
                                    model: historicalDatesModel
                                    displayText: currentIndex >= 0 ? model.get(currentIndex).text : "选择日期"
                                    font.pixelSize: 12

                                    onActivated: {
                                        if (currentIndex >= 0) {
                                            var dateStr = model.get(currentIndex).value
                                            console.log("选择的日期字符串:", dateStr)
                                            loadHistoryData(dateStr)
                                        }
                                    }

                                    // 自定义ComboBox样式
                                    background: Rectangle {
                                        color: "#ffffff"
                                        border.color: "#cccccc"
                                        border.width: 1
                                        radius: 3
                                    }

                                    contentItem: Text {
                                        text: historicalDateCombo.displayText
                                        font: historicalDateCombo.font
                                        color: "#333333"
                                        verticalAlignment: Text.AlignVCenter
                                        leftPadding: 8
                                        rightPadding: 20
                                    }

                                    // 自定义下拉箭头
                                    indicator: Rectangle {
                                        x: historicalDateCombo.width - width - 5
                                        y: historicalDateCombo.topPadding + (historicalDateCombo.availableHeight - height) / 2
                                        width: 12
                                        height: 8
                                        color: "transparent"

                                        Canvas {
                                            anchors.fill: parent
                                            onPaint: {
                                                var ctx = getContext("2d")
                                                ctx.reset()
                                                ctx.moveTo(2, 2)
                                                ctx.lineTo(width - 2, 2)
                                                ctx.lineTo(width / 2, height - 2)
                                                ctx.closePath()
                                                ctx.fillStyle = "#666666"
                                                ctx.fill()
                                            }
                                        }
                                    }

                                    // 自定义下拉列表样式
                                    popup: Popup {
                                        y: historicalDateCombo.height - 1
                                        width: historicalDateCombo.width
                                        implicitHeight: contentItem.implicitHeight
                                        padding: 1

                                        contentItem: ListView {
                                            clip: true
                                            implicitHeight: contentHeight
                                            model: historicalDateCombo.popup.visible ? historicalDateCombo.delegateModel : null
                                            currentIndex: historicalDateCombo.highlightedIndex

                                            ScrollIndicator.vertical: ScrollIndicator { }
                                        }

                                        background: Rectangle {
                                            color: "#ffffff"
                                            border.color: "#cccccc"
                                            border.width: 1
                                            radius: 3
                                        }
                                    }

                                    // 自定义选项样式
                                    delegate: ItemDelegate {
                                        width: historicalDateCombo.width
                                        height: 30

                                        contentItem: Text {
                                            text: model.text || ""
                                            color: "#333333"
                                            font: historicalDateCombo.font
                                            elide: Text.ElideRight
                                            verticalAlignment: Text.AlignVCenter
                                            leftPadding: 8
                                        }

                                        highlighted: historicalDateCombo.highlightedIndex === index

                                        background: Rectangle {
                                            color: highlighted ? "#e3f2fd" : "#ffffff"
                                            border.color: highlighted ? "#2196f3" : "transparent"
                                            border.width: highlighted ? 1 : 0
                                            radius: 2
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                historicalDateCombo.currentIndex = index
                                                historicalDateCombo.activated(index)
                                                historicalDateCombo.popup.close()
                                            }
                                        }
                                    }
                                }

                                Button {
                                    text: "刷新"
                                    Layout.preferredWidth: 50
                                    Layout.preferredHeight: 30
                                    font.pixelSize: 10

                                    onClicked: {
                                        refreshHistoryDates()
                                    }

                                    background: Rectangle {
                                        color: parent.pressed ? "#e0e0e0" : "#f5f5f5"
                                        border.color: "#cccccc"
                                        border.width: 1
                                        radius: 3
                                    }
                                }

                                Button {
                                    text: "返回实时"
                                    Layout.preferredWidth: 80
                                    Layout.preferredHeight: 30
                                    font.pixelSize: 10
                                    visible: isHistoryMode

                                    onClicked: {
                                        switchToRealTimeMode()
                                    }

                                    background: Rectangle {
                                        color: parent.pressed ? "#388e3c" : "#4caf50"
                                        radius: 3
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        font.pixelSize: parent.font.pixelSize
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                }
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            // 显示当前选择的锅炉信息
                            RowLayout {
                                spacing: 8

                                Label {
                                    text: "当前: " + selectedBoilerUI
                                    font.pixelSize: 14
                                    font.bold: true
                                    color: "#2196f3"
                                }

                                // 简化的连接状态指示器
                                Rectangle {
                                    width: 12
                                    height: 12
                                    radius: 6
                                    color: "#4caf50"
                                    visible: selectedBoilerUI !== ""
                                }

                                Label {
                                    text: "已连接"
                                    font.pixelSize: 12
                                    color: "#4caf50"
                                    visible: selectedBoilerUI !== ""
                                }
                            }
                        }
                    }

                    // 烟气数据区域
                    Rectangle {
                        Layout.fillWidth: true
                        height: 580
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 20
                            spacing: 15

                            RowLayout {
                                Layout.fillWidth: true

                                Label {
                                    text: "烟气检测设备实时数据"
                                    font.pixelSize: 20
                                    font.bold: true
                                    color: "#333333"
                                    Layout.fillWidth: true
                                }

                                Rectangle {
                                    width: 12
                                    height: 12
                                    radius: 6
                                    color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                }

                                Label {
                                    text: monitorWindow.dataSource.connectionStatus
                                    font.pixelSize: 14
                                    color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                    font.bold: true
                                }
                            }

                            // 当前烟气数据显示
                            Rectangle {
                                Layout.fillWidth: true
                                height: 80
                                color: monitorWindow.dataSource.isDataConnected ? "#e8f5e8" : "#ffebee"
                                radius: 8
                                border.color: monitorWindow.dataSource.isDataConnected ? "#4caf50" : "#f44336"
                                border.width: 1

                                // 根据连接状态显示不同内容
                                StackLayout {
                                    anchors.fill: parent
                                    anchors.margins: 15
                                    currentIndex: monitorWindow.dataSource.isDataConnected ? 0 : 1

                                    // 有数据连接时显示实际数据
                                    RowLayout {
                                        spacing: 30

                                        // 氧量
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "氧量："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.smokeTableData.length > 0 ?
                                                      (monitorWindow.dataSource.smokeTableData[0].o2 + "%") : "--"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#388e3c"
                                            }
                                        }

                                        // CO含量
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "CO含量："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.smokeTableData.length > 0 ?
                                                      (monitorWindow.dataSource.smokeTableData[0].co + "ppm") : "--"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#ff9800"
                                            }
                                        }

                                        // 冷凝器温度
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "冷凝器温度："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.currentTemperature || "0.0℃"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#e91e63"
                                            }
                                        }

                                        // 压力表
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "压力表："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.currentVoltage || "0.0kPa"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#2196f3"
                                            }
                                        }

                                        // 抽气泵电流
                                        RowLayout {
                                            spacing: 8
                                            Label {
                                                text: "抽气泵电流："
                                                font.pixelSize: 14
                                                color: "#333333"
                                                font.bold: true
                                            }
                                            Label {
                                                text: monitorWindow.dataSource.currentCurrent || "0.000A"
                                                font.pixelSize: 16
                                                font.bold: true
                                                color: "#ff5722"
                                            }
                                        }


                                    }

                                    // 无数据连接时显示提示信息
                                    ColumnLayout {
                                        anchors.centerIn: parent
                                        spacing: 10

                                        Label {
                                            text: "⚠️ 未接入数据采集设备"
                                            font.pixelSize: 18
                                            font.bold: true
                                            color: "#f44336"
                                            Layout.alignment: Qt.AlignHCenter
                                        }

                                        Label {
                                            text: "请检查串口连接和设备配置"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.alignment: Qt.AlignHCenter
                                        }
                                    }
                                }
                            }

                            // 图表容器，包含图表和滚动条
                            ColumnLayout {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                spacing: 0

                                // 图表和滚动条的容器
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    color: "transparent"

                                    // 简化的ChartView，用于Qt 5.12兼容性
                                    ChartView {
                                        id: smokeChart
                                        anchors.fill: parent
                                        antialiasing: true
                                        backgroundColor: "#f8f9fa"

                                    title: {
                                        var timeLabel = ""
                                        switch (currentTimeRange) {
                                            case 60:
                                                timeLabel = "1小时"
                                                break
                                            case 480:
                                                timeLabel = "8小时"
                                                break
                                            case 720:
                                                timeLabel = "12小时"
                                                break
                                            case 1440:
                                                timeLabel = "24小时"
                                                break
                                            default:
                                                timeLabel = "1小时"
                                                break
                                        }
                                        var modeLabel = isHistoryMode ? "历史数据" : "实时数据"
                                        var dateLabel = isHistoryMode ? " - " + Qt.formatDate(currentHistoryDate, "yyyy-MM-dd") : ""
                                        return "烟气检测设备" + modeLabel + "曲线 (" + timeLabel + ")" + dateLabel
                                    }
                                    titleFont.pixelSize: 16
                                    titleFont.bold: true

                                    legend.alignment: Qt.AlignBottom
                                    legend.font.pixelSize: 12

                                    // 缩放相关属性已删除，仅保留UI样式



                                    ValueAxis {
                                        id: smokeAxisX
                                        // 优化：统一使用分钟作为时间单位
                                        titleText: "时间 (分钟)"

                                        // 动态调整显示范围
                                        property double windowSize: currentTimeRange  // 动态时间窗口

                                        tickCount: {
                                            // 根据时间范围设置合适的刻度数量
                                            switch (currentTimeRange) {
                                                case 60:    // 1小时，每10分钟一个刻度：0, 10, 20, 30, 40, 50, 60
                                                    return 7
                                                case 480:   // 8小时，每小时一个刻度：0, 60, 120, 180, 240, 300, 360, 420, 480
                                                    return 9
                                                case 720:   // 12小时，每2小时一个刻度：0, 120, 240, 360, 480, 600, 720
                                                    return 7
                                                case 1440:  // 24小时，每4小时一个刻度：0, 240, 480, 720, 960, 1200, 1440
                                                    return 7
                                                default:
                                                    return 7
                                            }
                                        }

                                        labelFormat: "%.0f"  // 显示整数分钟

                                        // 动态X轴范围 - 根据选择的时间范围调整
                                        min: 0
                                        max: windowSize  // 动态时间范围
                                    }

                                    // 相对时间轴不需要复杂的时间范围计算
                                    // X轴范围已经在ValueAxis中直接设置

                                    // 缩放变化处理已删除

                                // 左侧Y轴：O₂ 氧量 (0-25%)
                                ValueAxis {
                                    id: smokeAxisY_Left
                                    min: 0
                                    max: 25
                                    titleText: "O₂ (%)"
                                    labelFormat: "%.1f"
                                    color: "#388e3c"
                                }

                                // 右侧Y轴：CO (0-7000)
                                ValueAxis {
                                    id: smokeAxisY_Right
                                    min: 0
                                    max: 7000
                                    titleText: "CO(ppm)"
                                    labelFormat: "%.0f"
                                    color: "#666666"
                                }



                                LineSeries {
                                    id: smokeO2Series
                                    name: "O₂ (%)"
                                    color: "#388e3c"
                                    width: 2
                                    axisX: smokeAxisX
                                    axisY: smokeAxisY_Left
                                }

                                LineSeries {
                                    id: smokeCOSeries
                                    name: "CO (ppm)"
                                    color: "#ff9800"
                                    width: 2
                                    axisX: smokeAxisX
                                    axisYRight: smokeAxisY_Right
                                }

                                // 简化的数据更新信号连接
                                Connections {
                                    target: monitorWindow.dataSource
                                    function onChartDataUpdated() {
                                        // 禁用信号触发的更新，避免与定时器冲突
                                        // 只在设备切换时才进行全量更新
                                    }
                                }

                                    // 鼠标交互区域
                                    MouseArea {
                                        anchors.fill: parent
                                        acceptedButtons: Qt.NoButton
                                        hoverEnabled: true
                                    }

                                    // 相对时间轴不需要定时更新

                                    Component.onCompleted: {
                                        // 初始化时使用全量更新 - 简化为30分钟视图
                                        fullUpdateChartData()
                                    }
                                }
                            }

                                // 缩放控制按钮已删除

                                // 时间范围选择控件
                                RowLayout {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 40
                                    spacing: 15

                                    Label {
                                        text: "时间范围:"
                                        font.pixelSize: 12
                                        color: "#666666"
                                    }

                                    // 动态生成时间范围按钮
                                    Repeater {
                                        model: timeRangeOptions

                                        Button {
                                            text: modelData.label
                                            Layout.preferredWidth: 70
                                            Layout.preferredHeight: 30
                                            font.pixelSize: 10

                                            property bool isSelected: currentTimeRange === modelData.minutes

                                            onClicked: {
                                                switchTimeRange(modelData.minutes)
                                            }

                                            background: Rectangle {
                                                color: parent.isSelected ? "#2196f3" : (parent.pressed ? "#e0e0e0" : "#f5f5f5")
                                                border.color: parent.isSelected ? "#1976d2" : "#cccccc"
                                                border.width: 1
                                                radius: 3
                                            }

                                            contentItem: Text {
                                                text: parent.text
                                                color: parent.isSelected ? "white" : "#333333"
                                                font.pixelSize: parent.font.pixelSize
                                                font.bold: parent.isSelected
                                                horizontalAlignment: Text.AlignHCenter
                                                verticalAlignment: Text.AlignVCenter
                                            }
                                        }
                                    }

                                    Item {
                                        Layout.fillWidth: true
                                    }

                                    Label {
                                        text: {
                                            var timeLabel = ""
                                            switch (currentTimeRange) {
                                                case 60:
                                                    timeLabel = "1小时"
                                                    break
                                                case 480:
                                                    timeLabel = "8小时"
                                                    break
                                                case 720:
                                                    timeLabel = "12小时"
                                                    break
                                                case 1440:
                                                    timeLabel = "24小时"
                                                    break
                                                default:
                                                    timeLabel = "1小时"
                                                    break
                                            }
                                            return "当前视图: " + timeLabel
                                        }
                                        font.pixelSize: 11
                                        color: "#666666"
                                        font.bold: true
                                    }
                                }

                                // 历史数据滑块控制
                                RowLayout {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 40
                                    spacing: 15
                                    visible: shouldShowSlider()

                                    Label {
                                        text: "数据范围:"
                                        font.pixelSize: 12
                                        color: "#666666"
                                    }

                                    Slider {
                                        id: historySlider
                                        Layout.fillWidth: true
                                        Layout.preferredHeight: 30
                                        from: 0.0
                                        to: 1.0
                                        value: sliderPosition

                                        onValueChanged: {
                                            if (isHistoryMode && Math.abs(value - sliderPosition) > 0.01) {
                                                sliderPosition = value
                                                updateHistoryChart()
                                            }
                                        }

                                        background: Rectangle {
                                            x: historySlider.leftPadding
                                            y: historySlider.topPadding + historySlider.availableHeight / 2 - height / 2
                                            implicitWidth: 200
                                            implicitHeight: 4
                                            width: historySlider.availableWidth
                                            height: implicitHeight
                                            radius: 2
                                            color: "#e0e0e0"

                                            Rectangle {
                                                width: historySlider.visualPosition * parent.width
                                                height: parent.height
                                                color: "#2196f3"
                                                radius: 2
                                            }
                                        }

                                        handle: Rectangle {
                                            x: historySlider.leftPadding + historySlider.visualPosition * (historySlider.availableWidth - width)
                                            y: historySlider.topPadding + historySlider.availableHeight / 2 - height / 2
                                            implicitWidth: 20
                                            implicitHeight: 20
                                            radius: 10
                                            color: historySlider.pressed ? "#1976d2" : "#2196f3"
                                            border.color: "#ffffff"
                                            border.width: 2
                                        }
                                    }

                                    Label {
                                        text: {
                                            if (!isHistoryMode || historyDataPointCount === 0) {
                                                return ""
                                            }
                                            var pointsNeeded = getPointsNeededForTimeRange(currentTimeRange)
                                            var totalPoints = historyDataPointCount
                                            var maxStartIndex = Math.max(0, totalPoints - pointsNeeded)
                                            var currentStartIndex = Math.round(sliderPosition * maxStartIndex)
                                            var endIndex = Math.min(currentStartIndex + pointsNeeded, totalPoints)
                                            return currentStartIndex + "-" + endIndex + "/" + totalPoints
                                        }
                                        font.pixelSize: 11
                                        color: "#666666"
                                        Layout.preferredWidth: 80
                                    }
                                }
                            }
                        }
                    }

                    // 烟气数据表格
                    Rectangle {
                        Layout.fillWidth: true
                        height: 350
                        color: "#ffffff"
                        radius: 12
                        border.color: "#e0e0e0"
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 20
                            spacing: 15

                            RowLayout {
                                Layout.fillWidth: true

                                Label {
                                    text: "烟气数据记录"
                                    font.pixelSize: 18
                                    font.bold: true
                                    color: "#333333"
                                    Layout.fillWidth: true
                                }

                                Label {
                                    text: {
                                        var count = monitorWindow.dataSource.smokeTableData.length
                                        return "实时数据 (" + count + " 条记录)"
                                    }
                                    font.pixelSize: 12
                                    color: "#666666"
                                }
                            }

                            // 固定表头
                            Rectangle {
                                Layout.fillWidth: true
                                height: 40
                                color: "#e8f5e8"
                                border.color: "#4caf50"
                                border.width: 1
                                z: 1

                                Row {
                                    anchors.fill: parent
                                    anchors.margins: 10
                                    spacing: 10

                                    Label { text: "时间"; width: 160; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "氧量(%)"; width: 70; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "CO(ppm)"; width: 80; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "冷凝器温度(℃)"; width: 90; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "压力表(kPa)"; width: 80; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "抽气泵电流(A)"; width: 90; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                    Label { text: "反吹反馈"; width: 60; font.bold: true; color: "#2e7d32"; font.pixelSize: 11 }
                                }
                            }

                            Rectangle {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                color: "#ffffff"
                                border.color: "#e0e0e0"
                                border.width: 1

                                StackLayout {
                                    anchors.fill: parent
                                    currentIndex: (monitorWindow.dataSource.isDataConnected && monitorWindow.dataSource.smokeTableData.length > 0) ? 0 : 1

                                    // 有数据时显示表格
                                    ListView {
                                        model: monitorWindow.dataSource.smokeTableData
                                        clip: true
                                        boundsBehavior: Flickable.StopAtBounds

                                        // 防止滚动事件向上传播到外层ScrollView
                                        flickableDirection: Flickable.VerticalFlick
                                        interactive: true

                                        // 滚动条
                                        ScrollBar.vertical: ScrollBar {
                                            active: true
                                            policy: ScrollBar.AsNeeded
                                        }

                                        // 添加鼠标区域来拦截滚动事件
                                        MouseArea {
                                            anchors.fill: parent
                                            acceptedButtons: Qt.NoButton
                                            onWheel: {
                                                // 检查ListView是否可以处理滚动
                                                var listView = parent
                                                var canScrollUp = listView.contentY > 0
                                                var canScrollDown = listView.contentY < (listView.contentHeight - listView.height)

                                                // 如果ListView可以滚动，则阻止事件传播
                                                if ((wheel.angleDelta.y > 0 && canScrollUp) ||
                                                    (wheel.angleDelta.y < 0 && canScrollDown)) {
                                                    wheel.accepted = true

                                                    // 手动处理ListView滚动
                                                    var delta = wheel.angleDelta.y
                                                    var scrollAmount = delta > 0 ? -30 : 30  // 滚动步长
                                                    listView.contentY = Math.max(0,
                                                        Math.min(listView.contentHeight - listView.height,
                                                                listView.contentY + scrollAmount))
                                                } else {
                                                    // ListView已到边界，允许事件传播给外层ScrollView
                                                    wheel.accepted = false
                                                }
                                            }
                                        }

                                        delegate: Rectangle {
                                            width: parent ? parent.width : 800  // 防止parent为null的错误
                                            height: 35
                                            color: index % 2 === 0 ? "#ffffff" : "#f5f5f5"
                                            border.color: "#e0e0e0"
                                            border.width: index === 0 ? 0 : 1

                                            Row {
                                                anchors.fill: parent
                                                anchors.margins: 10
                                                spacing: 10

                                                Label {
                                                    text: modelData.time
                                                    width: 160
                                                    font.pixelSize: 10
                                                    color: "#333333"
                                                }
                                                Label {
                                                    text: modelData.o2 + "%"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#388e3c"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: modelData.co + "ppm"
                                                    width: 80
                                                    font.pixelSize: 10
                                                    color: "#ff9800"
                                                    font.bold: true
                                                }

                                                Label {
                                                    text: (modelData.temperature || "0.0") + "℃"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#e91e63"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: (modelData.voltage || "0.0") + "V"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#2196f3"
                                                    font.bold: true
                                                }
                                                Label {
                                                    text: (modelData.current || "0.000") + "A"
                                                    width: 70
                                                    font.pixelSize: 10
                                                    color: "#ff5722"
                                                    font.bold: true
                                                }

                                            }
                                        }
                                    }

                                    // 无数据时显示提示
                                    ColumnLayout {
                                        anchors.centerIn: parent
                                        spacing: 15

                                        Label {
                                            text: "📊 暂无数据记录"
                                            font.pixelSize: 16
                                            font.bold: true
                                            color: "#999999"
                                            Layout.alignment: Qt.AlignHCenter
                                        }

                                        Label {
                                            text: monitorWindow.dataSource.isDataConnected ?
                                                  "等待数据采集..." : "请先连接数据采集设备"
                                            font.pixelSize: 14
                                            color: "#666666"
                                            Layout.alignment: Qt.AlignHCenter
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 简化的数据连接状态监听
    Connections {
        target: monitorWindow.dataSource
        function onCurrentBoilerChanged() {
            // 简单的状态同步，无需复杂的防抖逻辑
            // 数据源已经简化，直接同步即可
        }
    }

    // 刷新历史数据日期列表
    function refreshHistoryDates() {
        historicalDatesModel.clear()
        var dates = csvReader.getAvailableDates()
        console.log("获取到历史数据日期:", dates.length, "个")
        for (var i = 0; i < dates.length; i++) {
            console.log("添加日期:", dates[i])
            historicalDatesModel.append({
                text: dates[i],
                value: dates[i]
            })
        }
    }

    // 页面加载完成后自动开始监控
    Component.onCompleted: {
        // 启动实时监控
        monitorWindow.startMonitoring()

        // 初始化历史数据日期列表
        refreshHistoryDates()

        // 简化的初始化
        Qt.callLater(function() {
            // 触发图表初始化（使用全量更新）
            fullUpdateChartData()
        })

        // MonitoringDataSource现在使用智能检测机制，QML端不再需要额外的延迟
    }

    // 优化的清理函数 - 减少不必要的操作
    function performQuickCleanup() {
        // 停止监控
        if (monitorWindow.dataSource.isRunning) {
            monitorWindow.stopMonitoring()
        }

        // 只在必要时清理图表数据
        if (smokeO2Series.count > 0 || smokeCOSeries.count > 0) {
            smokeO2Series.clear()
            smokeCOSeries.clear()
        }

        // 重置状态
        resetPageState()
    }

    // 重置页面状态
    function resetPageState() {
        isReturningHome = false
    }

    // 完整清理资源的函数 - 保留作为备用
    function performCleanup() {
        performQuickCleanup()
    }

    // 页面销毁时清理资源（保留作为备用）
    Component.onDestruction: {
        // 如果页面被强制销毁，只执行最关键的清理
        if (monitorWindow.dataSource.isRunning) {
            monitorWindow.stopMonitoring()
        }
        chartUpdateTimer.stop()
    }

    // 历史数据相关的数据模型 - 仅保留UI样式
    ListModel {
        id: historicalDatesModel
    }

    ListModel {
        id: historicalDataModel
    }
}
