# DCS寄存器地址配置说明

## 概述

现在DCS通信已经简化为只发送SO2和NOx数据，所有寄存器地址都通过配置文件进行管理，无需修改代码。

## 配置文件设置

### config.ini 中的配置项

```ini
[DCS1]
; ... 其他配置 ...

; 写入寄存器配置 - 烟气分析仪数据发送到DCS的寄存器地址
SO2WriteAddress = 15         ; SO2数据写入DCS的寄存器地址
NOxWriteAddress = 17         ; NOx数据写入DCS的寄存器地址
```

## 如何修改寄存器地址

### 1. 修改SO2寄存器地址
如果DCS系统中SO2数据应该写入寄存器地址20，只需修改：
```ini
SO2WriteAddress = 20
```

### 2. 修改NOx寄存器地址
如果DCS系统中NOx数据应该写入寄存器地址22，只需修改：
```ini
NOxWriteAddress = 22
```

### 3. 重启程序
修改配置后，重启智慧燃烧程序即可生效。

## 发送的数据包格式

### 分离发送模式
现在SO2和NOx数据分别发送到各自的寄存器地址，支持非连续地址配置。

### SO2数据包格式
```
设备地址: 0C
功能码: 10 (写多个寄存器)
起始地址: [SO2WriteAddress的2字节]
寄存器数量: 00 02 (2个寄存器)
数据字节数: 04 (4字节)
SO2数据: [4字节IEEE 754浮点数]
CRC校验: [2字节]
```

### NOx数据包格式
```
设备地址: 0C
功能码: 10 (写多个寄存器)
起始地址: [NOxWriteAddress的2字节]
寄存器数量: 00 02 (2个寄存器)
数据字节数: 04 (4字节)
NOx数据: [4字节IEEE 754浮点数]
CRC校验: [2字节]
```

### 完整示例
如果SO2WriteAddress=15，NOxWriteAddress=100，SO2=100.5，NOx=200.3：

**SO2数据包：**
```
0C 10 00 0F 00 02 04 42 C9 00 00 [CRC]
```

**NOx数据包：**
```
0C 10 00 64 00 02 04 43 48 4C CD [CRC]
```

## 寄存器地址说明

### 地址范围
- **SO2WriteAddress**: 可设置为任意有效的DCS寄存器地址
- **NOxWriteAddress**: 通常设置为SO2地址+2（因为每个浮点数占用2个寄存器）

### 地址计算
- 每个IEEE 754浮点数占用4字节 = 2个Modbus寄存器
- SO2数据占用寄存器：[SO2WriteAddress] 和 [SO2WriteAddress+1]
- NOx数据占用寄存器：[NOxWriteAddress] 和 [NOxWriteAddress+1]
- **重要**：SO2和NOx地址可以完全独立，不需要连续

## 调试信息

程序启动时会显示配置的地址：
```
DCS设备 'DCS1' 烟气分析仪数据写入地址 - SO2: 15, NOx: 100
配置验证: SO2WriteAddress=15, NOxWriteAddress=100
```

发送数据时会显示详细信息：
```
开始分别发送SO2和NOx数据到DCS
SO2写入地址: 15, NOx写入地址: 100
=== 发送SO2数据 ===
发送SO2数据包 - 地址: 15, 值: 100.5000: 0C 10 00 0F 00 02 04 42 C9 00 00 XX XX
DCS SO2数据发送成功
=== 发送NOx数据 ===
发送NOx数据包 - 地址: 100, 值: 200.3000: 0C 10 00 64 00 02 04 43 48 4C CD XX XX
DCS NOx数据发送成功
*** SO2和NOx数据分别发送成功 ***
```

## 常见配置示例

### 示例1：连续地址
```ini
SO2WriteAddress = 100
NOxWriteAddress = 102
```
- SO2占用寄存器100-101
- NOx占用寄存器102-103

### 示例2：分散地址（现在支持！）
```ini
SO2WriteAddress = 50
NOxWriteAddress = 200
```
- SO2占用寄存器50-51
- NOx占用寄存器200-201
- 地址完全独立，不需要连续

### 示例3：高地址范围
```ini
SO2WriteAddress = 1000
NOxWriteAddress = 1002
```
- SO2占用寄存器1000-1001
- NOx占用寄存器1002-1003

## 注意事项

1. **地址范围**：确保地址在DCS系统的有效范围内（通常0-65535）
2. **地址冲突**：避免与DCS系统中其他数据的寄存器地址冲突
3. **地址独立**：SO2和NOx地址可以完全独立，支持任意非连续地址
4. **权限**：确保DCS系统允许写入指定的寄存器地址
5. **测试**：修改地址后务必测试通信是否正常
6. **发送间隔**：两个数据包之间有50ms间隔，避免通信冲突

## 故障排除

### 如果发送失败
1. 检查寄存器地址是否在DCS允许的范围内
2. 确认DCS系统是否允许写入这些地址
3. 检查串口连接和通信参数
4. 查看调试日志中的错误信息

### 如果DCS收不到数据
1. 确认DCS系统中寄存器地址映射是否正确
2. 检查数据格式是否符合DCS要求
3. 验证Modbus通信参数（波特率、校验位等）
4. 使用串口调试工具验证数据包格式

## 总结

✅ **配置简单**：只需修改config.ini中的两个地址参数
✅ **无需编程**：不需要修改任何代码
✅ **灵活配置**：可以根据DCS系统要求随时调整地址
✅ **调试友好**：提供详细的日志信息便于排查问题
