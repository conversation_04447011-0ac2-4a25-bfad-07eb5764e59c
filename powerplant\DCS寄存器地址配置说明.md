# DCS寄存器地址配置说明

## 概述

现在DCS通信已经简化为只发送SO2和NOx数据，所有寄存器地址都通过配置文件进行管理，无需修改代码。

## 配置文件设置

### config.ini 中的配置项

```ini
[DCS1]
; ... 其他配置 ...

; 写入寄存器配置 - 烟气分析仪数据发送到DCS的寄存器地址
SO2WriteAddress = 15         ; SO2数据写入DCS的寄存器地址
NOxWriteAddress = 17         ; NOx数据写入DCS的寄存器地址
```

## 如何修改寄存器地址

### 1. 修改SO2寄存器地址
如果DCS系统中SO2数据应该写入寄存器地址20，只需修改：
```ini
SO2WriteAddress = 20
```

### 2. 修改NOx寄存器地址
如果DCS系统中NOx数据应该写入寄存器地址22，只需修改：
```ini
NOxWriteAddress = 22
```

### 3. 重启程序
修改配置后，重启智慧燃烧程序即可生效。

## 发送的数据包格式

### 当前配置下的数据包
```
设备地址: 0C
功能码: 10 (写多个寄存器)
起始地址: 00 0F (15，SO2WriteAddress)
寄存器数量: 00 04 (4个寄存器)
数据字节数: 08 (8字节)
SO2数据: [4字节IEEE 754浮点数]
NOx数据: [4字节IEEE 754浮点数]
CRC校验: [2字节]
```

### 完整示例
如果SO2=100.5，NOx=200.3，数据包为：
```
0C 10 00 0F 00 04 08 42 C9 00 00 43 48 4C CD [CRC]
```

## 寄存器地址说明

### 地址范围
- **SO2WriteAddress**: 可设置为任意有效的DCS寄存器地址
- **NOxWriteAddress**: 通常设置为SO2地址+2（因为每个浮点数占用2个寄存器）

### 地址计算
- 每个IEEE 754浮点数占用4字节 = 2个Modbus寄存器
- SO2数据占用寄存器：[SO2WriteAddress] 和 [SO2WriteAddress+1]
- NOx数据占用寄存器：[NOxWriteAddress] 和 [NOxWriteAddress+1]

## 调试信息

程序启动时会显示配置的地址：
```
DCS设备 'DCS1' 烟气分析仪数据写入地址 - SO2: 15, NOx: 17
配置验证: SO2WriteAddress=15, NOxWriteAddress=17
```

发送数据时会显示详细信息：
```
SO2数据 (100.5000) -> 42 C9 00 00
NOx数据 (200.3000) -> 43 48 4C CD
发送DCS SO2+NOx数据包，长度: 17字节
发送的数据是：0C 10 00 0F 00 04 08 42 C9 00 00 43 48 4C CD XX XX
```

## 常见配置示例

### 示例1：连续地址
```ini
SO2WriteAddress = 100
NOxWriteAddress = 102
```
- SO2占用寄存器100-101
- NOx占用寄存器102-103

### 示例2：分散地址
```ini
SO2WriteAddress = 50
NOxWriteAddress = 80
```
- SO2占用寄存器50-51
- NOx占用寄存器80-81

### 示例3：高地址范围
```ini
SO2WriteAddress = 1000
NOxWriteAddress = 1002
```
- SO2占用寄存器1000-1001
- NOx占用寄存器1002-1003

## 注意事项

1. **地址范围**：确保地址在DCS系统的有效范围内（通常0-65535）
2. **地址冲突**：避免与DCS系统中其他数据的寄存器地址冲突
3. **连续性**：建议NOx地址设置为SO2地址+2，保持数据连续性
4. **权限**：确保DCS系统允许写入指定的寄存器地址
5. **测试**：修改地址后务必测试通信是否正常

## 故障排除

### 如果发送失败
1. 检查寄存器地址是否在DCS允许的范围内
2. 确认DCS系统是否允许写入这些地址
3. 检查串口连接和通信参数
4. 查看调试日志中的错误信息

### 如果DCS收不到数据
1. 确认DCS系统中寄存器地址映射是否正确
2. 检查数据格式是否符合DCS要求
3. 验证Modbus通信参数（波特率、校验位等）
4. 使用串口调试工具验证数据包格式

## 总结

✅ **配置简单**：只需修改config.ini中的两个地址参数
✅ **无需编程**：不需要修改任何代码
✅ **灵活配置**：可以根据DCS系统要求随时调整地址
✅ **调试友好**：提供详细的日志信息便于排查问题
