#include "boiler.h"
#include <thread>
#include <chrono>
#include "csvfile.h"
#include "smoke_analyzer_comm.h"
#include "dcs.h"  // 添加DCS头文件
#ifdef _WIN32
    #include <windows.h>
    #define close _close
#else
    #include <unistd.h>
#endif

bool ready = false;

Boiler::Boiler(ConfigManager *config_manager,std::string boiler_name) {
    this->config_manager = config_manager;
    this->boiler_name = boiler_name;
    this->is_initialized = false;  // 初始化为false
    debug_printf("调试: 创建锅炉 '%s'\n", boiler_name.c_str());

    if (load_config() == 0) {
        this->is_initialized = true;
        debug_printf("调试: 锅炉 '%s' 配置加载成功\n", boiler_name.c_str());
    } else {
        debug_printf("错误: 锅炉 '%s' 配置加载失败\n", boiler_name.c_str());
    }
}

Boiler::~Boiler(){
    // 不再需要停止线程，因为使用detach()方式，线程独立运行
    // 只需要关闭文件描述符
    if (fd >= 0) {
        close(fd);
        debug_printf("锅炉 %s 析构，关闭文件描述符: %d\n", boiler_name.c_str(), fd);
    }
}

int Boiler::load_config(){
    //读取锅炉号配置
    debug_printf("调试: 加载锅炉 '%s' 的配置\n", boiler_name.c_str());
    protocol = config_manager->get<std::string>(boiler_name.c_str(),"Protocol");

    // 检查配置文件中是否存在CollectionInterval字段
    if (!config_manager->exists(boiler_name.c_str(), "CollectionInterval")) {
        debug_printf("错误: 锅炉 '%s' 配置文件中缺少 CollectionInterval 字段\n", boiler_name.c_str());
        return -1;  // 返回错误
    }
    collection_interval = config_manager->get<int>(boiler_name.c_str(),"CollectionInterval", 0);  // 使用0作为占位符默认值

    // 验证采集间隔的有效性
    if (collection_interval <= 0) {
        debug_printf("错误: 锅炉 '%s' 的采集间隔配置无效: %d秒\n", boiler_name.c_str(), collection_interval);
        return -1;  // 返回错误
    }

    device_nox = config_manager->get<int>(boiler_name.c_str(),"Nox");
    device_current = config_manager->get<int>(boiler_name.c_str(),"Current");
    device_so2 = config_manager->get<int>(boiler_name.c_str(),"So2");
    device_voltage = config_manager->get<int>(boiler_name.c_str(),"Voltage");
    device_tempature = config_manager->get<int>(boiler_name.c_str(),"Tempature");
    device_o2 = config_manager->get<int>(boiler_name.c_str(),"O2");
    device_co = config_manager->get<int>(boiler_name.c_str(),"Co");

    debug_printf("调试: 锅炉 '%s' 配置 - 协议: %s, 采集间隔: %d秒, CO设备: %d, O2设备: %d\n",
           boiler_name.c_str(), protocol.c_str(), collection_interval, device_co, device_o2);
    return 0;
}

void Boiler::do_data_collect(Boiler *boiler){
    unsigned char receive_buffer[256];
    int bytes_received;

    //打开串口设备
    int fd = boiler->fd;
    if (fd < 0) {
        debug_printf("***锅炉 %s 串口打开失败，文件描述符: %d\n", boiler->boiler_name.c_str(), fd);
        ready = true;
        return;
    }

    debug_printf("锅炉 %s 数据采集线程启动，文件描述符: %d\n", boiler->boiler_name.c_str(), fd);

    // 烟气分析仪不再创建独立的CSV文件，数据将由DCS设备统一管理
    // 持续采集数据，线程独立运行直到程序结束
    int count = 0;
    while (true) {
        //采集数据
        bytes_received = boiler->read_data(fd, receive_buffer, sizeof(receive_buffer));

        // 烟气分析仪数据不再直接写入CSV，而是存储在内存中供DCS设备使用
        // 数据已经通过read_data()函数更新到boiler对象的成员变量中

        count++;
        if (count % 10 == 0) {
            debug_printf("锅炉 %s 已采集 %d 次数据，采集间隔: %d秒\n", boiler->boiler_name.c_str(), count, boiler->collection_interval);
        }

        std::this_thread::sleep_for(std::chrono::seconds(boiler->collection_interval)); // 使用配置的采集间隔
    }

    debug_printf("锅炉 %s 数据采集线程退出\n", boiler->boiler_name.c_str());
    ready = true;
}

void Boiler::start_data_collect(){
    std::thread t(&Boiler::do_data_collect, this, this); // 绑定 this 指针和参数
    debug_printf("%s waiting collect finished...\n", this->boiler_name.c_str());
    t.detach(); // 使用detach方式，与hello项目保持一致
}

// stop_data_collect函数已移除，因为使用detach()方式，线程独立运行直到程序结束




//采集数据
int Boiler::read_data(int fd, unsigned char *buffer, int length) {
    if (fd<0) {
        // 串口未连接，使用锁保护地设置无效数据
        {
            std::lock_guard<std::mutex> lock(rwMutex);
            co = o2 = so2 = nox = current = voltage = temperature = 0.0f;
        }
        debug_printf("串口未连接，无法采集数据\n");
        return 0; // 返回0表示没有数据
    }

    // 在开始采集前清理串口缓冲区，避免DCS数据干扰
    clear_serial_buffer(fd);

    // 临时变量存储读取的数据
    float temp_co = 0.0f, temp_o2 = 0.0f, temp_so2 = 0.0f, temp_nox = 0.0f;
    float temp_current = 0.0f, temp_voltage = 0.0f, temp_temperature = 0.0f;
    int count;
    unsigned int registers[10];

    // 读取所有数据（增加设备间隔时间，避免DCS干扰）
    // CO读取
    for (int retry = 0; retry < 2; retry++) {
        count = read_holding_registers(fd, device_co, 0x0000, 1, registers,false);
        if (count > 0) {
            temp_co = registers[0];  // 直接使用原始值，不乘以0.01
            debug_printf("co:%.2f 读取成功\n",temp_co);
            break;
        } else if (retry == 0) {
            debug_printf("CO读取失败，重试中...\n");
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(300));

    // O2读取
    for (int retry = 0; retry < 2; retry++) {
        count = read_holding_registers(fd, device_o2, 0x0000, 1, registers,false);
        if (count > 0) {
            temp_o2 = registers[0]*0.01;
            debug_printf("==o2:%.2f 读取成功\n",temp_o2);
            break;
        } else if (retry == 0) {
            debug_printf("O2读取失败，重试中...\n");
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(300));

    // SO2和NOx读取 - 原有方式
    for (int retry = 0; retry < 2; retry++) {
        count = read_holding_registers(fd, device_so2, 0x1000, 6, registers,true);
        if (count > 0) {
            temp_so2 = hex_to_float(registers[0]);
            temp_nox = hex_to_float(registers[1]);
            debug_printf("==so2:%.2f,nox=%.2f 读取成功\n",temp_so2,temp_nox);
            break;
        } else if (retry == 0) {
            debug_printf("SO2/NOx读取失败，重试中...\n");
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(300));

    // 电流读取
    for (int retry = 0; retry < 2; retry++) {
        count = read_holding_registers(fd, device_current, 0x0000, 1, registers,false);
        if (count > 0) {
            temp_current = registers[0]*0.001;
            debug_printf("==current:%.2f 读取成功\n",temp_current);
            break;
        } else if (retry == 0) {
            debug_printf("电流读取失败，重试中...\n");
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(300));

    // 电压读取
    for (int retry = 0; retry < 2; retry++) {
        count = read_holding_registers(fd, device_voltage, 0x1000, 1, registers,false);
        if (count > 0) {
            temp_voltage = registers[0]*0.1;
            debug_printf("==voltage:%.2f 读取成功\n",temp_voltage);
            break;
        } else if (retry == 0) {
            debug_printf("电压读取失败，重试中...\n");
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(300));

    // 温度读取
    for (int retry = 0; retry < 2; retry++) {
        count = read_holding_registers(fd, device_tempature, 0x0000, 1, registers,false);
        if (count > 0) {
            temp_temperature = registers[0]*0.1;
            debug_printf("==temprature:%.2f 读取成功\n",temp_temperature);
            break;
        } else if (retry == 0) {
            debug_printf("温度读取失败，重试中...\n");
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }

    // 更新成员变量（使用互斥锁保证线程安全）
    {
        std::lock_guard<std::mutex> lock(rwMutex);
        co = temp_co;
        o2 = temp_o2;
        so2 = temp_so2;
        nox = temp_nox;
        current = temp_current;
        voltage = temp_voltage;
        temperature = temp_temperature;
    }

    // 调试：显示所有采集到的数据
    debug_printf("锅炉 %s 本次采集数据: CO=%.2f, O2=%.2f, SO2=%.2f, NOx=%.2f, Current=%.2f, Voltage=%.2f, Temp=%.2f\n",
                boiler_name.c_str(), temp_co, temp_o2, temp_so2, temp_nox, temp_current, temp_voltage, temp_temperature);

    // 如果获取到有效的SO2和NOx数据，立即发送给DCS
    // 修改条件：确保SO2和NOx都大于0才发送，避免发送无效数据
    if (temp_so2 > 0 && temp_nox > 0) {
        debug_printf("锅炉 %s 获取到有效烟气分析仪数据: SO2=%.2f, NOx=%.2f，准备发送给DCS\n",
                    boiler_name.c_str(), temp_so2, temp_nox);

        // 查找关联的DCS设备并发送数据
        extern std::unordered_map<std::string, DCSDevice*> dcs_map;
        for (const auto& dcs_pair : dcs_map) {
            DCSDevice* dcs_device = dcs_pair.second;
            if (dcs_device && dcs_device->associated_boiler == boiler_name) {
                debug_printf("找到关联的DCS设备 '%s'，发送数据\n", dcs_device->dcs_name.c_str());

                // 使用静态变量控制发送频率，避免过于频繁
                static int send_counter = 0;
                send_counter++;
                if (send_counter == 1 || send_counter % 3 == 0) {  // 第一次立即发送，之后每3次采集发送一次
                    debug_printf("发送烟气分析仪数据到DCS (counter=%d)\n", send_counter);
                    debug_printf("DCS设备信息: 名称='%s', fd=%d, 设备地址=%d\n",
                                dcs_device->dcs_name.c_str(), dcs_device->fd, dcs_device->device_address);
                    debug_printf("写入地址配置: SO2地址=%d, NOx地址=%d\n",
                                dcs_device->so2_write_address, dcs_device->nox_write_address);

                    int result = dcs_device->send_analyzer_data_to_dcs(temp_so2, temp_nox);
                    if (result == 0) {
                        debug_printf("烟气分析仪数据发送到DCS '%s' 成功\n", dcs_device->dcs_name.c_str());
                    } else {
                        debug_printf("烟气分析仪数据发送到DCS '%s' 失败，错误码=%d\n", dcs_device->dcs_name.c_str(), result);
                    }
                } else {
                    debug_printf("跳过发送，等待下一个发送周期 (counter=%d)\n", send_counter);
                }
                break;  // 找到第一个关联的DCS设备就退出
            }
        }
    }


    //TimeStamp,O2,CO,NOx,SO2,T,V,I
    int data_length = snprintf((char *)buffer, length, "%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f",
                               o2, co, nox, so2, temperature, voltage,current);
    return data_length;
}


