@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title SmartBurning Monitor
set "exeName=SmartBurning.exe"
set checkCount=0

:loop
set /a checkCount+=1
tasklist /FI "IMAGENAME eq %exeName%" 2>NUL | find /I "%exeName%" >NUL
if %errorlevel% neq 0 (
    echo [%time%] 第 !checkCount! 次检测：程序未运行，正在启动...
    start "" "%cd%\%exeName%"
    echo [%time%] 已启动新进程
) else (
    echo [%time%] 第 !checkCount! 次检测：程序运行中
)
timeout /t 20 /nobreak >nul
goto loop
