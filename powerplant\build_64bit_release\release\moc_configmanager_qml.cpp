/****************************************************************************
** Meta object code from reading C++ file 'configmanager_qml.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../configmanager_qml.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'configmanager_qml.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16ConfigManagerQMLE_t {};
} // unnamed namespace

template <> constexpr inline auto ConfigManagerQML::qt_create_metaobjectdata<qt_meta_tag_ZN16ConfigManagerQMLE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ConfigManagerQML",
        "configChanged",
        "",
        "configSaved",
        "success",
        "configReloaded",
        "getRS485Config",
        "QVariantMap",
        "getDCSRS485Config",
        "getDCSOPCConfig",
        "getParameterAdjustmentConfig",
        "saveRS485Config",
        "config",
        "saveDCSRS485Config",
        "saveDCSOPCConfig",
        "saveParameterAdjustmentConfig",
        "saveAllConfigs",
        "reloadConfigs",
        "restartApplication",
        "setConfigValue",
        "section",
        "key",
        "value",
        "getSmokeAnalyzerDeviceName"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'configChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'configSaved'
        QtMocHelpers::SignalData<void(bool)>(3, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 4 },
        }}),
        // Signal 'configReloaded'
        QtMocHelpers::SignalData<void(bool)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 4 },
        }}),
        // Method 'getRS485Config'
        QtMocHelpers::MethodData<QVariantMap()>(6, 2, QMC::AccessPublic, 0x80000000 | 7),
        // Method 'getDCSRS485Config'
        QtMocHelpers::MethodData<QVariantMap()>(8, 2, QMC::AccessPublic, 0x80000000 | 7),
        // Method 'getDCSOPCConfig'
        QtMocHelpers::MethodData<QVariantMap()>(9, 2, QMC::AccessPublic, 0x80000000 | 7),
        // Method 'getParameterAdjustmentConfig'
        QtMocHelpers::MethodData<QVariantMap()>(10, 2, QMC::AccessPublic, 0x80000000 | 7),
        // Method 'saveRS485Config'
        QtMocHelpers::MethodData<bool(const QVariantMap &)>(11, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { 0x80000000 | 7, 12 },
        }}),
        // Method 'saveDCSRS485Config'
        QtMocHelpers::MethodData<bool(const QVariantMap &)>(13, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { 0x80000000 | 7, 12 },
        }}),
        // Method 'saveDCSOPCConfig'
        QtMocHelpers::MethodData<bool(const QVariantMap &)>(14, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { 0x80000000 | 7, 12 },
        }}),
        // Method 'saveParameterAdjustmentConfig'
        QtMocHelpers::MethodData<bool(const QVariantMap &)>(15, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { 0x80000000 | 7, 12 },
        }}),
        // Method 'saveAllConfigs'
        QtMocHelpers::MethodData<bool()>(16, 2, QMC::AccessPublic, QMetaType::Bool),
        // Method 'reloadConfigs'
        QtMocHelpers::MethodData<bool()>(17, 2, QMC::AccessPublic, QMetaType::Bool),
        // Method 'restartApplication'
        QtMocHelpers::MethodData<bool()>(18, 2, QMC::AccessPublic, QMetaType::Bool),
        // Method 'setConfigValue'
        QtMocHelpers::MethodData<void(const QString &, const QString &, const QString &)>(19, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 20 }, { QMetaType::QString, 21 }, { QMetaType::QString, 22 },
        }}),
        // Method 'getSmokeAnalyzerDeviceName'
        QtMocHelpers::MethodData<QString()>(23, 2, QMC::AccessPublic, QMetaType::QString),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ConfigManagerQML, qt_meta_tag_ZN16ConfigManagerQMLE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ConfigManagerQML::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ConfigManagerQMLE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ConfigManagerQMLE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16ConfigManagerQMLE_t>.metaTypes,
    nullptr
} };

void ConfigManagerQML::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ConfigManagerQML *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->configChanged(); break;
        case 1: _t->configSaved((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 2: _t->configReloaded((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 3: { QVariantMap _r = _t->getRS485Config();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 4: { QVariantMap _r = _t->getDCSRS485Config();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 5: { QVariantMap _r = _t->getDCSOPCConfig();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 6: { QVariantMap _r = _t->getParameterAdjustmentConfig();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 7: { bool _r = _t->saveRS485Config((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 8: { bool _r = _t->saveDCSRS485Config((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 9: { bool _r = _t->saveDCSOPCConfig((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 10: { bool _r = _t->saveParameterAdjustmentConfig((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 11: { bool _r = _t->saveAllConfigs();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 12: { bool _r = _t->reloadConfigs();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 13: { bool _r = _t->restartApplication();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 14: _t->setConfigValue((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 15: { QString _r = _t->getSmokeAnalyzerDeviceName();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ConfigManagerQML::*)()>(_a, &ConfigManagerQML::configChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ConfigManagerQML::*)(bool )>(_a, &ConfigManagerQML::configSaved, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ConfigManagerQML::*)(bool )>(_a, &ConfigManagerQML::configReloaded, 2))
            return;
    }
}

const QMetaObject *ConfigManagerQML::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ConfigManagerQML::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ConfigManagerQMLE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ConfigManagerQML::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void ConfigManagerQML::configChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ConfigManagerQML::configSaved(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void ConfigManagerQML::configReloaded(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}
QT_WARNING_POP
