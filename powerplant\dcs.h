#ifndef DCS_H
#define DCS_H

#include <string>
#include <mutex>
#include <vector>
#include <unordered_map>
#include "config_manager.h"

class DCSDevice {
public:
    DCSDevice(ConfigManager *config_manager, std::string dcs_name);
    ~DCSDevice();
    
    // 加载配置
    int load_config();
    
    // 采集数据
    int read_data();
    
    // 启动采集线程
    void start_data_collect();

    // 线程函数
    void do_data_collect();
    
    // 从缓冲区解析浮点数
    float parse_float(const unsigned char* buffer, int offset);

    // 将浮点数转换为IEEE 754格式的4字节数组
    void float_to_ieee754_bytes(float value, unsigned char* bytes);

    // 写入单个寄存器到DCS
    int write_single_register(unsigned short register_address, unsigned short value);

    // 发送烟气分析仪数据到DCS（只发送SO2和NOx数据）
    int send_analyzer_data_to_dcs(float so2_value, float nox_value);

    // 配置项
    ConfigManager *config_manager;
    std::string dcs_name;
    std::string desc;
    std::string protocol;
    int collection_interval;
    std::string associated_boiler;  // 关联的锅炉名称
    
    // DCS设备Modbus配置
    unsigned char device_address;
    unsigned short start_register;
    unsigned short register_count;
    
    // 数据偏移量配置
    int furnace_pressure_offset;
    int superheater_temp_offset;
    int generator_power_offset;
    int main_steam_pressure_offset;
    int total_air_flow_offset;
    int water_coal_ratio_offset;
    int primary_fan_a_offset;
    int primary_fan_b_offset;
    int fan_a_offset;
    int fan_b_offset;
    int induced_fan_a_offset;
    int induced_fan_b_offset;
    int co_offset;
    int o2_offset;
    int so2_offset;
    int nox_offset;

    // 烟气分析仪数据写入DCS的寄存器地址
    unsigned short so2_write_address;
    unsigned short nox_write_address;

    // 数据值
    float furnace_pressure;       // 炉膛压力
    float superheater_temp;       // 过热器平均温度
    float generator_power;        // 发电机功率
    float main_steam_pressure;    // 主蒸汽压力
    float total_air_flow;         // 总风量
    float water_coal_ratio;       // 水煤比
    float primary_fan_a;          // 一次分机A入口导叶位置
    float primary_fan_b;          // 一次分机B入口导叶位置
    float fan_a;                  // 送风机A入口导叶位置
    float fan_b;                  // 送风机B入口导叶位置
    float induced_fan_a;          // 引风机A入口导叶位置
    float induced_fan_b;          // 引风机B入口导叶位置
    float co;                     // CO
    float o2;                     // O2
    
    // 原始数据缓冲区
    std::vector<unsigned char> data_buffer;
    
    // 采集文件句柄
    int fd;
    
    // 初始化状态标志
    bool is_initialized;
    
    // 互斥锁
    std::mutex rwMutex;
};

// 外部变量声明
extern std::unordered_map<std::string, DCSDevice*> dcs_map;

// 函数声明
std::unordered_map<std::string, DCSDevice*> get_dcs_list(ConfigManager *config_manager);
void get_realtime_dcs_data(std::string dcs_name,
                          float *furnace_pressure,
                          float *superheater_temp,
                          float *generator_power,
                          float *main_steam_pressure,
                          float *total_air_flow,
                          float *water_coal_ratio,
                          float *co,
                          float *o2,
                          float *primary_fan_a,
                          float *primary_fan_b,
                          float *fan_a,
                          float *fan_b,
                          float *induced_fan_a,
                          float *induced_fan_b);

#endif // DCS_H