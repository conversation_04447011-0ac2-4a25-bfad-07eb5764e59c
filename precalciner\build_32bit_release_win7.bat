@echo off
chcp 65001 >nul
echo ========================================
echo Build PrecalcinerBurning 32-bit RELEASE version (Windows 7+ Compatible)
echo ========================================

echo Setting 32-bit Qt environment variables for Windows 7+...
set QT32_PATH=D:\Development\Qt32\5.12 MinGW32\Qt\5.12.0\mingw73_32
set MINGW32_PATH=D:\Development\Qt32\5.12 MinGW32\Qt\Tools\mingw730_32\bin
set PATH=%QT32_PATH%\bin;%MINGW32_PATH%;C:\Windows\System32;C:\Windows

echo Current directory: %CD%
echo Qt path: %QT32_PATH%
echo MinGW32 path: %MINGW32_PATH%
echo Target OS: Windows 7+ (32-bit)

echo.
echo Cleaning old build files...
if exist build_32bit_release_win7 rmdir /s /q build_32bit_release_win7
mkdir build_32bit_release_win7
cd build_32bit_release_win7

echo.
echo Step 1: Running qmake to generate Makefile (RELEASE mode for Windows 7+)...
qmake ..\PrecalcinerBurning.pro -spec win32-g++ "CONFIG+=release" "CONFIG-=debug" "CONFIG-=qml_debug"

if %ERRORLEVEL% neq 0 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Building the project (RELEASE for Windows 7+)...
mingw32-make

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Copying necessary DLL files and scripts...
copy "..\DACLTSDK.dll" "release\"
copy "..\borlndmm.dll" "release\"
copy "..\config.ini" "release\"
copy "..\重启软件.bat" "release\"
copy "..\水泥厂分解炉软件崩溃检测脚本.bat" "release\"

echo.
echo Step 3.5: Copying essential runtime DLLs for Windows 7+...
copy "%MINGW32_PATH%\libgcc_s_dw2-1.dll" "release\" 2>nul
copy "%MINGW32_PATH%\libstdc++-6.dll" "release\" 2>nul
copy "%MINGW32_PATH%\libwinpthread-1.dll" "release\" 2>nul

echo.
echo Step 3.6: Copying Qt platform plugins...
if not exist "release\platforms" mkdir "release\platforms"
copy "%QT32_PATH%\plugins\platforms\qwindows.dll" "release\platforms\" 2>nul

echo.
echo Step 4: Running windeployqt (RELEASE mode optimized for Windows 7+)...
windeployqt --release --no-translations --no-system-d3d-compiler --no-opengl-sw --qmldir .. release\PrecalcinerBurning.exe

echo.
echo Step 5: Windows 7+ specific optimizations...
echo Removing unnecessary image format plugins...
if exist "release\imageformats\qicns.dll" del "release\imageformats\qicns.dll"
if exist "release\imageformats\qtga.dll" del "release\imageformats\qtga.dll"
if exist "release\imageformats\qtiff.dll" del "release\imageformats\qtiff.dll"
if exist "release\imageformats\qwbmp.dll" del "release\imageformats\qwbmp.dll"
if exist "release\imageformats\qwebp.dll" del "release\imageformats\qwebp.dll"

echo Removing unnecessary bearer plugins...
if exist "release\bearer" rmdir /s /q "release\bearer"

echo Removing unnecessary platform input contexts...
if exist "release\platforminputcontexts" rmdir /s /q "release\platforminputcontexts"

echo Keeping Windows 7 compatible styles...
echo Note: Keeping qwindowsvistastyle.dll for Windows 7 compatibility

echo Removing QML debugging tools...
if exist "release\qmltooling" rmdir /s /q "release\qmltooling"

echo Removing translation files...
if exist "release\translations" rmdir /s /q "release\translations"

echo.
echo Step 6: Creating Windows 7+ deployment package...
if not exist "release\README_Win7.txt" (
    echo Creating Windows 7+ deployment information...
    echo This package is optimized for Windows 7 and later versions. > "release\README_Win7.txt"
    echo Minimum requirements: >> "release\README_Win7.txt"
    echo - Windows 7 SP1 32-bit or 64-bit >> "release\README_Win7.txt"
    echo - Visual C++ Redistributable for Visual Studio 2015-2019 >> "release\README_Win7.txt"
    echo - At least 2GB RAM >> "release\README_Win7.txt"
    echo - DirectX 9.0c or later >> "release\README_Win7.txt"
    echo. >> "release\README_Win7.txt"
    echo Built with Qt 5.12.0 and MinGW 7.3.0 >> "release\README_Win7.txt"
    echo Build date: %DATE% %TIME% >> "release\README_Win7.txt"
)

echo.
echo ========================================
echo Windows 7+ RELEASE build completed successfully!
echo ========================================
echo Executable: build_32bit_release_win7\release\PrecalcinerBurning.exe
echo.
echo Target OS: Windows 7, 8, 8.1, 10, 11 (32-bit and 64-bit)
echo Qt Version: 5.12.0
echo Compiler: MinGW 7.3.0
echo.
echo Size comparison:
powershell -Command "Get-ChildItem 'release' -Recurse | Measure-Object -Property Length -Sum | ForEach-Object { 'Total size: ' + [math]::Round($_.Sum/1MB,2) + ' MB' }"
echo ========================================

cd ..
echo.
echo Build output directory: build_32bit_release_win7\release\
echo Ready for deployment on Windows 7+ systems!
pause
