@echo off
chcp 65001 >nul
echo ========================================
echo Build SmartBurning 64-bit RELEASE version
echo ========================================

echo Setting 64-bit Qt environment variables...
set QT64_PATH=D:\Development\Qt\6.9.1\mingw_64
set MINGW64_PATH=D:\Development\Qt\Tools\mingw1310_64\bin
set PATH=%QT64_PATH%\bin;%MINGW64_PATH%;C:\Windows\System32;C:\Windows

echo Current directory: %CD%
echo Qt path: %QT64_PATH%
echo MinGW64 path: %MINGW64_PATH%

echo.
echo Cleaning old build files...
if exist build_64bit_release rmdir /s /q build_64bit_release
mkdir build_64bit_release
cd build_64bit_release

echo.
echo Step 1: Running qmake to generate Makefile (RELEASE mode)...
qmake ..\SmartBurning.pro -spec win32-g++ "CONFIG+=release" "CONFIG-=debug" "CONFIG-=qml_debug"

if %ERRORLEVEL% neq 0 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Building the project (RELEASE)...
mingw32-make

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Copying necessary files and scripts...
copy "..\config.ini" "release\"
copy "..\重启软件.bat" "release\"
copy "..\电厂智慧燃烧软件崩溃检测脚本.bat" "release\"

echo.
echo Step 3.5: Copying essential runtime DLLs...
copy "%MINGW64_PATH%\libgcc_s_seh-1.dll" "release\" 2>nul
copy "%MINGW64_PATH%\libstdc++-6.dll" "release\" 2>nul
copy "%MINGW64_PATH%\libwinpthread-1.dll" "release\" 2>nul

echo.
echo Step 3.6: Copying Qt platform plugins...
if not exist "release\platforms" mkdir "release\platforms"
copy "%QT64_PATH%\plugins\platforms\qwindows.dll" "release\platforms\" 2>nul

echo.
echo Step 4: Running windeployqt (RELEASE mode with optimizations)...
windeployqt --release --no-translations --no-system-d3d-compiler --no-opengl-sw --qmldir .. release\SmartBurning.exe

echo.
echo Step 5: Cleaning up unnecessary files to reduce size...
echo Removing unnecessary image format plugins...
if exist "release\imageformats\qicns.dll" del "release\imageformats\qicns.dll"
if exist "release\imageformats\qtga.dll" del "release\imageformats\qtga.dll"
if exist "release\imageformats\qtiff.dll" del "release\imageformats\qtiff.dll"
if exist "release\imageformats\qwbmp.dll" del "release\imageformats\qwbmp.dll"
if exist "release\imageformats\qwebp.dll" del "release\imageformats\qwebp.dll"

echo Removing unnecessary bearer plugins...
if exist "release\bearer" rmdir /s /q "release\bearer"

echo Removing unnecessary platform input contexts...
if exist "release\platforminputcontexts" rmdir /s /q "release\platforminputcontexts"

echo Removing unnecessary styles (keeping only essential)...
if exist "release\styles\qwindowsvistastyle.dll" del "release\styles\qwindowsvistastyle.dll"

echo Removing QML debugging tools...
if exist "release\qmltooling" rmdir /s /q "release\qmltooling"

echo Removing translation files...
if exist "release\translations" rmdir /s /q "release\translations"

echo.
echo ========================================
echo RELEASE build completed successfully!
echo ========================================
echo Executable: build_64bit_release\release\SmartBurning.exe
echo.
echo Size comparison:
powershell -Command "Get-ChildItem 'release' -Recurse | Measure-Object -Property Length -Sum | ForEach-Object { 'Total size: ' + [math]::Round($_.Sum/1MB,2) + ' MB' }"
echo ========================================

cd ..
pause
