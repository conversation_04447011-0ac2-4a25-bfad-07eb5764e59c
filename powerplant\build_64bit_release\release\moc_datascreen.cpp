/****************************************************************************
** Meta object code from reading C++ file 'datascreen.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../datascreen.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'datascreen.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN10DataScreenE_t {};
} // unnamed namespace

template <> constexpr inline auto DataScreen::qt_create_metaobjectdata<qt_meta_tag_ZN10DataScreenE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "DataScreen",
        "messageChanged",
        "",
        "dataChanged",
        "isRunningChanged",
        "boilerListChanged",
        "currentBoilerChanged",
        "dcsListChanged",
        "currentDcsChanged",
        "dataConnectionChanged",
        "setIsRunning",
        "running",
        "setCurrentBoiler",
        "boiler",
        "setCurrentDcs",
        "dcs",
        "startMonitoring",
        "stopMonitoring",
        "updateData",
        "message",
        "payload",
        "mainSteamPressure",
        "mainSteamTemp",
        "reheatSteamPressure",
        "reheatSteamTemp",
        "mainSteamFlow",
        "totalFuel",
        "totalAir",
        "oxygenContent",
        "furnacePressure",
        "coContent",
        "noxContent",
        "so2Content",
        "primaryFanA",
        "primaryFanB",
        "fanA",
        "fanB",
        "inducedFanA",
        "inducedFanB",
        "isRunning",
        "boilerList",
        "currentBoiler",
        "dcsList",
        "currentDcs",
        "isDataConnected",
        "connectionStatus"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'messageChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'dataChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isRunningChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'boilerListChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentBoilerChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'dcsListChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentDcsChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'dataConnectionChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'setIsRunning'
        QtMocHelpers::SlotData<void(bool)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 11 },
        }}),
        // Slot 'setCurrentBoiler'
        QtMocHelpers::SlotData<void(const QString &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 13 },
        }}),
        // Slot 'setCurrentDcs'
        QtMocHelpers::SlotData<void(const QString &)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 15 },
        }}),
        // Slot 'startMonitoring'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'stopMonitoring'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'updateData'
        QtMocHelpers::SlotData<void()>(18, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'message'
        QtMocHelpers::PropertyData<QString>(19, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 0),
        // property 'payload'
        QtMocHelpers::PropertyData<QString>(20, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'mainSteamPressure'
        QtMocHelpers::PropertyData<QString>(21, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'mainSteamTemp'
        QtMocHelpers::PropertyData<QString>(22, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'reheatSteamPressure'
        QtMocHelpers::PropertyData<QString>(23, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'reheatSteamTemp'
        QtMocHelpers::PropertyData<QString>(24, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'mainSteamFlow'
        QtMocHelpers::PropertyData<QString>(25, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'totalFuel'
        QtMocHelpers::PropertyData<QString>(26, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'totalAir'
        QtMocHelpers::PropertyData<QString>(27, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'oxygenContent'
        QtMocHelpers::PropertyData<QString>(28, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'furnacePressure'
        QtMocHelpers::PropertyData<QString>(29, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'oxygenContent'
        QtMocHelpers::PropertyData<QString>(28, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'coContent'
        QtMocHelpers::PropertyData<QString>(30, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'noxContent'
        QtMocHelpers::PropertyData<QString>(31, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'so2Content'
        QtMocHelpers::PropertyData<QString>(32, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'primaryFanA'
        QtMocHelpers::PropertyData<QString>(33, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'primaryFanB'
        QtMocHelpers::PropertyData<QString>(34, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'fanA'
        QtMocHelpers::PropertyData<QString>(35, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'fanB'
        QtMocHelpers::PropertyData<QString>(36, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'inducedFanA'
        QtMocHelpers::PropertyData<QString>(37, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'inducedFanB'
        QtMocHelpers::PropertyData<QString>(38, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'isRunning'
        QtMocHelpers::PropertyData<bool>(39, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 2),
        // property 'boilerList'
        QtMocHelpers::PropertyData<QStringList>(40, QMetaType::QStringList, QMC::DefaultPropertyFlags, 3),
        // property 'currentBoiler'
        QtMocHelpers::PropertyData<QString>(41, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 4),
        // property 'dcsList'
        QtMocHelpers::PropertyData<QStringList>(42, QMetaType::QStringList, QMC::DefaultPropertyFlags, 5),
        // property 'currentDcs'
        QtMocHelpers::PropertyData<QString>(43, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 6),
        // property 'isDataConnected'
        QtMocHelpers::PropertyData<bool>(44, QMetaType::Bool, QMC::DefaultPropertyFlags, 7),
        // property 'connectionStatus'
        QtMocHelpers::PropertyData<QString>(45, QMetaType::QString, QMC::DefaultPropertyFlags, 7),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<DataScreen, qt_meta_tag_ZN10DataScreenE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject DataScreen::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10DataScreenE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10DataScreenE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN10DataScreenE_t>.metaTypes,
    nullptr
} };

void DataScreen::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<DataScreen *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->messageChanged(); break;
        case 1: _t->dataChanged(); break;
        case 2: _t->isRunningChanged(); break;
        case 3: _t->boilerListChanged(); break;
        case 4: _t->currentBoilerChanged(); break;
        case 5: _t->dcsListChanged(); break;
        case 6: _t->currentDcsChanged(); break;
        case 7: _t->dataConnectionChanged(); break;
        case 8: _t->setIsRunning((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 9: _t->setCurrentBoiler((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 10: _t->setCurrentDcs((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 11: _t->startMonitoring(); break;
        case 12: _t->stopMonitoring(); break;
        case 13: _t->updateData(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (DataScreen::*)()>(_a, &DataScreen::messageChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (DataScreen::*)()>(_a, &DataScreen::dataChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (DataScreen::*)()>(_a, &DataScreen::isRunningChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (DataScreen::*)()>(_a, &DataScreen::boilerListChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (DataScreen::*)()>(_a, &DataScreen::currentBoilerChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (DataScreen::*)()>(_a, &DataScreen::dcsListChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (DataScreen::*)()>(_a, &DataScreen::currentDcsChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (DataScreen::*)()>(_a, &DataScreen::dataConnectionChanged, 7))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QString*>(_v) = _t->message(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->payload(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->mainSteamPressure(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->mainSteamTemp(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->reheatSteamPressure(); break;
        case 5: *reinterpret_cast<QString*>(_v) = _t->reheatSteamTemp(); break;
        case 6: *reinterpret_cast<QString*>(_v) = _t->mainSteamFlow(); break;
        case 7: *reinterpret_cast<QString*>(_v) = _t->totalFuel(); break;
        case 8: *reinterpret_cast<QString*>(_v) = _t->totalAir(); break;
        case 9: *reinterpret_cast<QString*>(_v) = _t->oxygenContent(); break;
        case 10: *reinterpret_cast<QString*>(_v) = _t->furnacePressure(); break;
        case 11: *reinterpret_cast<QString*>(_v) = _t->oxygenContent(); break;
        case 12: *reinterpret_cast<QString*>(_v) = _t->coContent(); break;
        case 13: *reinterpret_cast<QString*>(_v) = _t->noxContent(); break;
        case 14: *reinterpret_cast<QString*>(_v) = _t->so2Content(); break;
        case 15: *reinterpret_cast<QString*>(_v) = _t->primaryFanA(); break;
        case 16: *reinterpret_cast<QString*>(_v) = _t->primaryFanB(); break;
        case 17: *reinterpret_cast<QString*>(_v) = _t->fanA(); break;
        case 18: *reinterpret_cast<QString*>(_v) = _t->fanB(); break;
        case 19: *reinterpret_cast<QString*>(_v) = _t->inducedFanA(); break;
        case 20: *reinterpret_cast<QString*>(_v) = _t->inducedFanB(); break;
        case 21: *reinterpret_cast<bool*>(_v) = _t->isRunning(); break;
        case 22: *reinterpret_cast<QStringList*>(_v) = _t->boilerList(); break;
        case 23: *reinterpret_cast<QString*>(_v) = _t->currentBoiler(); break;
        case 24: *reinterpret_cast<QStringList*>(_v) = _t->dcsList(); break;
        case 25: *reinterpret_cast<QString*>(_v) = _t->currentDcs(); break;
        case 26: *reinterpret_cast<bool*>(_v) = _t->isDataConnected(); break;
        case 27: *reinterpret_cast<QString*>(_v) = _t->connectionStatus(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setMessage(*reinterpret_cast<QString*>(_v)); break;
        case 21: _t->setIsRunning(*reinterpret_cast<bool*>(_v)); break;
        case 23: _t->setCurrentBoiler(*reinterpret_cast<QString*>(_v)); break;
        case 25: _t->setCurrentDcs(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *DataScreen::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DataScreen::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10DataScreenE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int DataScreen::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 14;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 28;
    }
    return _id;
}

// SIGNAL 0
void DataScreen::messageChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void DataScreen::dataChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void DataScreen::isRunningChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void DataScreen::boilerListChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void DataScreen::currentBoilerChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void DataScreen::dcsListChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void DataScreen::currentDcsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void DataScreen::dataConnectionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}
QT_WARNING_POP
