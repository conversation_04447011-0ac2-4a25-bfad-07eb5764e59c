# DCS通信功能码修正说明

## 问题描述

在原始代码中，智慧燃烧系统向DCS发送数据时错误地使用了功能码`0x03`（读取保持寄存器），这在Modbus协议中是不正确的。

## 抓包数据分析

### 原始抓包数据
```
[10:54:54.257]收←◆0C 03 00 01 00 20 14 CF   // DCS发送的读取请求
[10:54:54.864]收←◆0C 03 00 01 00 20 14 CF   // 重复请求
[10:54:55.803]发→◇0C 03 40 3F 80 00 00 40 00 00 00 40 40 00 00 40 80 00 00 40 A0 00 00 40 C0 00 00 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 50 04
```

### 数据解析
- `0C` = 设备地址（12）
- `03` = 功能码（读取保持寄存器）- **这里是问题所在**
- `3F 80 00 00` = IEEE 754格式的浮点数 1.0
- `40 40 00 00` = IEEE 754格式的浮点数 3.0
- `50 04` = Modbus CRC校验

## 问题根源

### 通信方向混淆
1. **DCS → 智慧燃烧系统**（数据采集）：
   - DCS作为主站，发送读取请求：`0C 03 00 01 00 20 14 CF`
   - 功能码03正确（读取保持寄存器）
   - 智慧燃烧系统作为从站响应

2. **智慧燃烧系统 → DCS**（数据发送）：
   - 智慧燃烧系统发送数据到DCS
   - **错误使用**功能码03（读取）
   - **应该使用**功能码10（写多个寄存器）

## 修正方案

### 修改前（错误）
```cpp
data_packet[packet_len++] = device_address;  // 0x0C
data_packet[packet_len++] = 0x03;           // 错误：读取功能码
data_packet[packet_len++] = 0x40;           // 数据长度
```

### 修改后（正确）
```cpp
data_packet[packet_len++] = device_address;  // 0x0C
data_packet[packet_len++] = 0x10;           // 正确：写多个寄存器
data_packet[packet_len++] = 0x00;           // 起始寄存器地址高字节
data_packet[packet_len++] = 0x01;           // 起始寄存器地址低字节
data_packet[packet_len++] = 0x00;           // 寄存器数量高字节
data_packet[packet_len++] = 0x20;           // 寄存器数量低字节（32个寄存器）
data_packet[packet_len++] = 0x40;           // 数据字节数（64字节）
```

## Modbus功能码说明

| 功能码 | 名称 | 用途 | 方向 |
|--------|------|------|------|
| 0x03 | 读取保持寄存器 | 读取数据 | 主站→从站（请求） |
| 0x06 | 写单个寄存器 | 写入单个寄存器 | 主站→从站（写入） |
| 0x10 | 写多个寄存器 | 写入多个寄存器 | 主站→从站（写入） |

## 报文格式变化

### 修改前格式（错误）
```
0C 03 40 [64字节数据] [CRC]
总长度：67字节
```

### 修改后格式（正确 - 分别发送SO2和NOx）
**SO2数据包：**
```
0C 10 [SO2地址] 00 02 04 [SO2数据4字节] [CRC]
总长度：13字节
```

**NOx数据包：**
```
0C 10 [NOx地址] 00 02 04 [NOx数据4字节] [CRC]
总长度：13字节
```

### 新格式详解
**每个数据包格式：**
- `0C`: 设备地址
- `10`: 功能码（写多个寄存器）
- `[地址]`: 寄存器起始地址（2字节，来自配置）
- `00 02`: 寄存器数量（2个寄存器，每个2字节）
- `04`: 数据字节数（4字节：1个浮点数）
- `[数据4字节]`: 浮点数（IEEE 754格式）
- `[CRC]`: 校验码（2字节）

**优势：**
- 支持非连续寄存器地址
- 每个数据独立发送，更灵活
- 配置更简单直观

## 修改的文件

1. `dcs.cpp` - 修正并简化发送函数：
   - **删除**：`send_float_data_to_dcs()` （重复函数，未被使用）
   - **修正**：`send_analyzer_data_to_dcs()` （实际使用的函数）

2. `dcs.h` - 删除了重复函数的声明

## 代码简化

原来有两个功能相同的发送函数，现在只保留一个：
- ✅ **保留**：`send_analyzer_data_to_dcs()` - 被 `boiler.cpp` 实际调用
- ❌ **删除**：`send_float_data_to_dcs()` - 重复代码，无调用

## 验证方法

修改后，发送的数据包应该是：
```
0C 10 [SO2地址2字节] 00 04 08 [SO2数据4字节] [NOx数据4字节] [CRC 2字节]
```

例如，如果SO2地址是15（0x000F），SO2=100.5，NOx=200.3：
```
0C 10 00 0F 00 04 08 42 C9 00 00 43 48 4C CD [CRC]
```

这符合标准的Modbus写多个寄存器协议格式。

## 注意事项

1. **DCS端配置**：确保DCS系统配置为接受写入请求
2. **寄存器映射**：确认DCS中SO2和NOx寄存器地址的映射关系
3. **数据格式**：保持IEEE 754浮点数格式不变
4. **通信测试**：修改后需要重新测试通信是否正常
5. **地址配置**：检查配置文件中的`SO2WriteAddress`和`NOxWriteAddress`设置

## 总结

这个修正解决了Modbus协议使用不当的问题，使智慧燃烧系统能够正确地向DCS发送数据，而不是错误地发送读取命令。
