# DCS通信功能码修正说明

## 问题描述

在原始代码中，智慧燃烧系统向DCS发送数据时错误地使用了功能码`0x03`（读取保持寄存器），这在Modbus协议中是不正确的。

## 抓包数据分析

### 原始抓包数据
```
[10:54:54.257]收←◆0C 03 00 01 00 20 14 CF   // DCS发送的读取请求
[10:54:54.864]收←◆0C 03 00 01 00 20 14 CF   // 重复请求
[10:54:55.803]发→◇0C 03 40 3F 80 00 00 40 00 00 00 40 40 00 00 40 80 00 00 40 A0 00 00 40 C0 00 00 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 00 00 00 01 50 04
```

### 数据解析
- `0C` = 设备地址（12）
- `03` = 功能码（读取保持寄存器）- **这里是问题所在**
- `3F 80 00 00` = IEEE 754格式的浮点数 1.0
- `40 40 00 00` = IEEE 754格式的浮点数 3.0
- `50 04` = Modbus CRC校验

## 问题根源

### 通信方向混淆
1. **DCS → 智慧燃烧系统**（数据采集）：
   - DCS作为主站，发送读取请求：`0C 03 00 01 00 20 14 CF`
   - 功能码03正确（读取保持寄存器）
   - 智慧燃烧系统作为从站响应

2. **智慧燃烧系统 → DCS**（数据发送）：
   - 智慧燃烧系统发送数据到DCS
   - **错误使用**功能码03（读取）
   - **应该使用**功能码10（写多个寄存器）

## 修正方案

### 修改前（错误）
```cpp
data_packet[packet_len++] = device_address;  // 0x0C
data_packet[packet_len++] = 0x03;           // 错误：读取功能码
data_packet[packet_len++] = 0x40;           // 数据长度
```

### 修改后（正确）
```cpp
data_packet[packet_len++] = device_address;  // 0x0C
data_packet[packet_len++] = 0x10;           // 正确：写多个寄存器
data_packet[packet_len++] = 0x00;           // 起始寄存器地址高字节
data_packet[packet_len++] = 0x01;           // 起始寄存器地址低字节
data_packet[packet_len++] = 0x00;           // 寄存器数量高字节
data_packet[packet_len++] = 0x20;           // 寄存器数量低字节（32个寄存器）
data_packet[packet_len++] = 0x40;           // 数据字节数（64字节）
```

## Modbus功能码说明

| 功能码 | 名称 | 用途 | 方向 |
|--------|------|------|------|
| 0x03 | 读取保持寄存器 | 读取数据 | 主站→从站（请求） |
| 0x06 | 写单个寄存器 | 写入单个寄存器 | 主站→从站（写入） |
| 0x10 | 写多个寄存器 | 写入多个寄存器 | 主站→从站（写入） |

## 报文格式变化

### 修改前格式（错误）
```
0C 03 40 [64字节数据] [CRC]
总长度：67字节
```

### 修改后格式（正确）
```
0C 10 00 01 00 20 40 [64字节数据] [CRC]
总长度：73字节
```

### 新格式详解
- `0C`: 设备地址
- `10`: 功能码（写多个寄存器）
- `00 01`: 起始寄存器地址（地址1）
- `00 20`: 寄存器数量（32个寄存器，每个2字节）
- `40`: 数据字节数（64字节）
- `[64字节数据]`: 实际数据
- `[CRC]`: 校验码

## 修改的文件

1. `dcs.cpp` - 修正了两个发送函数：
   - `send_float_data_to_dcs()`
   - `send_analyzer_data_to_dcs()`

## 验证方法

修改后，发送的数据包应该是：
```
0C 10 00 01 00 20 40 [风机导叶位置数据24字节] [填充数据40字节] [CRC 2字节]
```

这符合标准的Modbus写多个寄存器协议格式。

## 注意事项

1. **DCS端配置**：确保DCS系统配置为接受写入请求
2. **寄存器映射**：确认DCS中寄存器地址1-32的映射关系
3. **数据格式**：保持IEEE 754浮点数格式不变
4. **通信测试**：修改后需要重新测试通信是否正常

## 总结

这个修正解决了Modbus协议使用不当的问题，使智慧燃烧系统能够正确地向DCS发送数据，而不是错误地发送读取命令。
